#!/usr/bin/env python3
"""
Trigonometric Functions Calculator

Implementation of trigonometric functions (sin, cos, tan) using mathematical algorithms
without relying on standard library functions. Uses Taylor series expansions and
other mathematical approaches.
"""

from typing import Union


def factorial(n: int) -> int:
    """
    Calculate factorial of a number.
    
    Args:
        n: Non-negative integer
        
    Returns:
        Factorial of n
    """
    if n < 0:
        raise ValueError("Factorial is not defined for negative numbers")
    if n <= 1:
        return 1
    
    result = 1
    for i in range(2, n + 1):
        result *= i
    return result


def power(base: float, exponent: int) -> float:
    """
    Calculate base raised to the power of exponent.
    
    Args:
        base: The base number
        exponent: The exponent (integer)
        
    Returns:
        base^exponent
    """
    if exponent == 0:
        return 1.0
    if exponent < 0:
        return 1.0 / power(base, -exponent)
    
    result = 1.0
    for _ in range(exponent):
        result *= base
    return result


def absolute_value(x: float) -> float:
    """
    Calculate absolute value of a number.
    
    Args:
        x: Input number
        
    Returns:
        Absolute value of x
    """
    return x if x >= 0 else -x


def pi_approximation(precision: int = 50) -> float:
    """
    Approximate π using the Leib<PERSON>z formula: π/4 = 1 - 1/3 + 1/5 - 1/7 + ...
    
    Args:
        precision: Number of terms to use in the series
        
    Returns:
        Approximation of π
    """
    pi_over_4 = 0.0
    sign = 1
    
    for i in range(precision):
        term = sign / (2 * i + 1)
        pi_over_4 += term
        sign *= -1
    
    return 4 * pi_over_4


# Pre-calculate π for use in angle normalization
PI = pi_approximation(1000)  # High precision π


def normalize_angle(angle: float) -> float:
    """
    Normalize angle to the range [-π, π].
    
    Args:
        angle: Angle in radians
        
    Returns:
        Normalized angle in range [-π, π]
    """
    # Reduce angle to range [0, 2π]
    while angle > PI:
        angle -= 2 * PI
    while angle < -PI:
        angle += 2 * PI
    return angle


def sin_taylor(x: float, terms: int = 20) -> float:
    """
    Calculate sine using Taylor series expansion.
    
    sin(x) = x - x³/3! + x⁵/5! - x⁷/7! + ...
    
    Args:
        x: Angle in radians
        terms: Number of terms in the Taylor series
        
    Returns:
        Sine of x
    """
    # Normalize angle to improve convergence
    x = normalize_angle(x)
    
    result = 0.0
    sign = 1
    
    for n in range(terms):
        exponent = 2 * n + 1
        term = sign * power(x, exponent) / factorial(exponent)
        result += term
        sign *= -1
        
        # Early termination if term becomes very small
        if absolute_value(term) < 1e-15:
            break
    
    return result


def cos_taylor(x: float, terms: int = 20) -> float:
    """
    Calculate cosine using Taylor series expansion.
    
    cos(x) = 1 - x²/2! + x⁴/4! - x⁶/6! + ...
    
    Args:
        x: Angle in radians
        terms: Number of terms in the Taylor series
        
    Returns:
        Cosine of x
    """
    # Normalize angle to improve convergence
    x = normalize_angle(x)
    
    result = 1.0  # First term is 1
    sign = -1     # Second term is negative
    
    for n in range(1, terms):
        exponent = 2 * n
        term = sign * power(x, exponent) / factorial(exponent)
        result += term
        sign *= -1
        
        # Early termination if term becomes very small
        if absolute_value(term) < 1e-15:
            break
    
    return result


def sin_cordic(x: float, iterations: int = 20) -> float:
    """
    Calculate sine using CORDIC algorithm (COordinate Rotation DIgital Computer).
    
    This is an alternative algorithm that uses only additions, subtractions,
    and bit shifts (approximated here with divisions by powers of 2).
    
    Args:
        x: Angle in radians
        iterations: Number of CORDIC iterations
        
    Returns:
        Sine of x
    """
    # Normalize angle
    x = normalize_angle(x)
    
    # CORDIC gain (approximately 1.646760258)
    K = 1.0
    for i in range(iterations):
        K *= (1 + power(2, -2 * i)) ** 0.5
    K = 1.0 / K
    
    # Initialize coordinates
    x_coord = K
    y_coord = 0.0
    z = x
    
    # Pre-calculated arctangent values: arctan(2^(-i))
    atan_table = [
        0.7853981633974483,  # arctan(1)
        0.4636476090008061,  # arctan(0.5)
        0.24497866312686414, # arctan(0.25)
        0.12435499454676144, # arctan(0.125)
        0.06241880999595735,
        0.031239833430268277,
        0.015623728620476831,
        0.007812341060101111,
        0.0039062301319669718,
        0.0019531225164788188,
        0.0009765621895593195,
        0.0004882812111948983,
        0.00024414062014936177,
        0.00012207031189367021,
        6.103515617420877e-05,
        3.0517578115526096e-05,
        1.5258789061315762e-05,
        7.62939453110197e-06,
        3.814697265606496e-06,
        1.907348632810187e-06
    ]
    
    for i in range(min(iterations, len(atan_table))):
        sigma = 1 if z >= 0 else -1
        
        new_x = x_coord - sigma * y_coord * power(2, -i)
        new_y = y_coord + sigma * x_coord * power(2, -i)
        new_z = z - sigma * atan_table[i]
        
        x_coord, y_coord, z = new_x, new_y, new_z
    
    return y_coord


def cos_cordic(x: float, iterations: int = 20) -> float:
    """
    Calculate cosine using CORDIC algorithm.
    
    Args:
        x: Angle in radians
        iterations: Number of CORDIC iterations
        
    Returns:
        Cosine of x
    """
    # Similar to sin_cordic but returns x_coord instead of y_coord
    x = normalize_angle(x)
    
    K = 1.0
    for i in range(iterations):
        K *= (1 + power(2, -2 * i)) ** 0.5
    K = 1.0 / K
    
    x_coord = K
    y_coord = 0.0
    z = x
    
    atan_table = [
        0.7853981633974483, 0.4636476090008061, 0.24497866312686414,
        0.12435499454676144, 0.06241880999595735, 0.031239833430268277,
        0.015623728620476831, 0.007812341060101111, 0.0039062301319669718,
        0.0019531225164788188, 0.0009765621895593195, 0.0004882812111948983,
        0.00024414062014936177, 0.00012207031189367021, 6.103515617420877e-05,
        3.0517578115526096e-05, 1.5258789061315762e-05, 7.62939453110197e-06,
        3.814697265606496e-06, 1.907348632810187e-06
    ]
    
    for i in range(min(iterations, len(atan_table))):
        sigma = 1 if z >= 0 else -1
        
        new_x = x_coord - sigma * y_coord * power(2, -i)
        new_y = y_coord + sigma * x_coord * power(2, -i)
        new_z = z - sigma * atan_table[i]
        
        x_coord, y_coord, z = new_x, new_y, new_z
    
    return x_coord


def tan_from_sin_cos(x: float, method: str = "taylor") -> float:
    """
    Calculate tangent as sin(x)/cos(x).
    
    Args:
        x: Angle in radians
        method: Algorithm to use ("taylor" or "cordic")
        
    Returns:
        Tangent of x
        
    Raises:
        ValueError: If cosine is zero (undefined tangent)
    """
    if method == "taylor":
        sin_val = sin_taylor(x)
        cos_val = cos_taylor(x)
    elif method == "cordic":
        sin_val = sin_cordic(x)
        cos_val = cos_cordic(x)
    else:
        raise ValueError("Method must be 'taylor' or 'cordic'")
    
    if absolute_value(cos_val) < 1e-10:
        raise ValueError("Tangent is undefined (cosine is zero)")
    
    return sin_val / cos_val


def degrees_to_radians(degrees: float) -> float:
    """
    Convert degrees to radians.
    
    Args:
        degrees: Angle in degrees
        
    Returns:
        Angle in radians
    """
    return degrees * PI / 180.0


def radians_to_degrees(radians: float) -> float:
    """
    Convert radians to degrees.

    Args:
        radians: Angle in radians

    Returns:
        Angle in degrees
    """
    return radians * 180.0 / PI


def compare_algorithms(angle_degrees: float) -> None:
    """
    Compare different trigonometric algorithms for a given angle.

    Args:
        angle_degrees: Angle in degrees to test
    """
    angle_rad = degrees_to_radians(angle_degrees)

    print(f"\nTrigonometric calculations for {angle_degrees}° ({angle_rad:.6f} radians):")
    print("=" * 70)

    # Sine calculations
    sin_taylor_val = sin_taylor(angle_rad)
    sin_cordic_val = sin_cordic(angle_rad)
    print(f"sin({angle_degrees}°):")
    print(f"  Taylor series:  {sin_taylor_val:.10f}")
    print(f"  CORDIC:         {sin_cordic_val:.10f}")
    print(f"  Difference:     {absolute_value(sin_taylor_val - sin_cordic_val):.2e}")

    # Cosine calculations
    cos_taylor_val = cos_taylor(angle_rad)
    cos_cordic_val = cos_cordic(angle_rad)
    print(f"\ncos({angle_degrees}°):")
    print(f"  Taylor series:  {cos_taylor_val:.10f}")
    print(f"  CORDIC:         {cos_cordic_val:.10f}")
    print(f"  Difference:     {absolute_value(cos_taylor_val - cos_cordic_val):.2e}")

    # Tangent calculations
    try:
        tan_taylor_val = tan_from_sin_cos(angle_rad, "taylor")
        tan_cordic_val = tan_from_sin_cos(angle_rad, "cordic")
        print(f"\ntan({angle_degrees}°):")
        print(f"  Taylor series:  {tan_taylor_val:.10f}")
        print(f"  CORDIC:         {tan_cordic_val:.10f}")
        print(f"  Difference:     {absolute_value(tan_taylor_val - tan_cordic_val):.2e}")
    except ValueError as e:
        print(f"\ntan({angle_degrees}°): {e}")


def main():
    """
    Demonstrate the trigonometric calculator with various algorithms.
    """
    print("Trigonometric Functions Calculator")
    print("=" * 50)
    print("Implementing sin, cos, tan without standard library functions")
    print("\nAlgorithms implemented:")
    print("1. Taylor Series Expansion")
    print("2. CORDIC Algorithm")
    print(f"\nUsing π ≈ {PI:.10f}")

    # Test common angles
    test_angles = [0, 30, 45, 60, 90, 120, 180, 270, 360]

    for angle in test_angles:
        compare_algorithms(angle)

    # Interactive mode
    print("\n" + "=" * 50)
    print("Interactive Mode")
    print("Commands:")
    print("  sin <angle>     - Calculate sine (degrees)")
    print("  cos <angle>     - Calculate cosine (degrees)")
    print("  tan <angle>     - Calculate tangent (degrees)")
    print("  compare <angle> - Compare all algorithms")
    print("  quit            - Exit")

    while True:
        try:
            command = input("\nEnter command: ").strip().lower()

            if command == "quit":
                break

            parts = command.split()
            if len(parts) != 2:
                print("Invalid command format. Use: <function> <angle>")
                continue

            func, angle_str = parts
            angle = float(angle_str)
            angle_rad = degrees_to_radians(angle)

            if func == "sin":
                taylor_result = sin_taylor(angle_rad)
                cordic_result = sin_cordic(angle_rad)
                print(f"sin({angle}°):")
                print(f"  Taylor: {taylor_result:.10f}")
                print(f"  CORDIC: {cordic_result:.10f}")
            elif func == "cos":
                taylor_result = cos_taylor(angle_rad)
                cordic_result = cos_cordic(angle_rad)
                print(f"cos({angle}°):")
                print(f"  Taylor: {taylor_result:.10f}")
                print(f"  CORDIC: {cordic_result:.10f}")
            elif func == "tan":
                try:
                    taylor_result = tan_from_sin_cos(angle_rad, "taylor")
                    cordic_result = tan_from_sin_cos(angle_rad, "cordic")
                    print(f"tan({angle}°):")
                    print(f"  Taylor: {taylor_result:.10f}")
                    print(f"  CORDIC: {cordic_result:.10f}")
                except ValueError as e:
                    print(f"tan({angle}°): {e}")
            elif func == "compare":
                compare_algorithms(angle)
            else:
                print("Unknown function. Use: sin, cos, tan, compare, or quit")

        except ValueError:
            print("Please enter a valid number for the angle")
        except KeyboardInterrupt:
            print("\nGoodbye!")
            break
        except Exception as e:
            print(f"Error: {e}")


if __name__ == "__main__":
    main()
