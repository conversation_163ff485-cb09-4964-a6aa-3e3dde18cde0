# Prime Number Calculator

A comprehensive Python application for prime number calculations featuring multiple algorithms for different use cases and performance requirements.

## Features

### Prime Number Algorithms

The calculator implements several algorithms for prime number operations:

#### Primality Testing
- **Basic Trial Division** (`is_prime`) - Simple deterministic algorithm
- **Miller-<PERSON>bin Test** (`miller_rabin_is_prime`) - Probabilistic algorithm, efficient for large numbers

#### Prime Generation
- **Sieve of Eratosthenes** (`sieve_of_eratosthenes`) - Classic algorithm for finding all primes up to a limit
- **Sieve of Sundaram** (`sieve_of_sundaram`) - Alternative sieve algorithm focusing on odd primes
- **Sequential Generation** (`first_n_primes`) - Generate the first N prime numbers

#### Prime Factorization
- **Prime Factors** (`prime_factors`) - Find all prime factors of a given number

## Usage

### Running the Program

```bash
python prime_calculator.py
```

The program includes both a demonstration mode and an interactive mode.

### Interactive Commands

- `check <number>` - Check if a number is prime (basic algorithm)
- `miller <number>` - Check if a number is prime (<PERSON><PERSON><PERSON> algorithm)
- `sieve <limit>` - Find all primes up to limit (Sieve of Eratosthenes)
- `sundaram <limit>` - Find all primes up to limit (Sieve of Sundaram)
- `first <count>` - Get the first N prime numbers
- `factors <number>` - Get prime factors of a number
- `quit` - Exit the program

### Example Usage

```python
from prime_calculator import is_prime, sieve_of_eratosthenes, miller_rabin_is_prime

# Check if a number is prime
print(is_prime(17))  # True
print(miller_rabin_is_prime(982451653))  # True (much faster for large numbers)

# Find all primes up to 30
primes = sieve_of_eratosthenes(30)
print(primes)  # [2, 3, 5, 7, 11, 13, 17, 19, 23, 29]

# Compare algorithms
primes_eratosthenes = sieve_of_eratosthenes(100)
primes_sundaram = sieve_of_sundaram(100)
print(primes_eratosthenes == primes_sundaram)  # True
```

## Algorithm Comparison

| Algorithm | Type | Best Use Case | Time Complexity |
|-----------|------|---------------|-----------------|
| Trial Division | Deterministic | Small numbers, single checks | O(√n) |
| Miller-Rabin | Probabilistic | Large numbers, cryptography | O(k log³ n) |
| Sieve of Eratosthenes | Deterministic | Multiple primes up to limit | O(n log log n) |
| Sieve of Sundaram | Deterministic | Alternative sieve method | O(n log log n) |

## Requirements

- Python 3.6+
- No external dependencies (uses only standard library)

## File Structure

```
prime_calculator.py    # Main application with all algorithms
README.md             # This documentation
```

## Technical Details

### Type Annotations
The code uses modern Python type hints for better code clarity and IDE support:

```python
primes: list[int] = []  # Explicitly typed list for prime numbers
```

### Algorithm Implementations

- **Miller-Rabin**: Implements the probabilistic primality test with configurable rounds
- **Sieve of Sundaram**: Generates odd primes using the mathematical property i + j + 2*i*j
- **Optimized Trial Division**: Uses square root optimization and even number skipping

## Contributing

Feel free to contribute additional algorithms or optimizations. Some potential enhancements:
- Segmented Sieve for very large ranges
- Pollard's rho algorithm for factorization
- Wheel factorization optimization
- Performance benchmarking suite

## License

This project is available for educational and research purposes.
