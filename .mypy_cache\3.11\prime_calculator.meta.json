{"data_mtime": 1754165555, "dep_lines": [16, 17, 18, 1, 1, 1, 1, 1], "dep_prios": [10, 10, 5, 5, 30, 30, 30, 30], "dependencies": ["math", "random", "typing", "builtins", "_frozen_importlib", "_typeshed", "abc", "typing_extensions"], "hash": "873e749692bd9e484dc38c15d81e75bd56ba0ff1", "id": "prime_calculator", "ignore_all": false, "interface_hash": "d2225e5d827e8050e1d8f6e1cd3c2fa7a8f48e1c", "mtime": 1754165611, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\home-repos\\svn_monitor_server_test_repo\\prime_calculator.py", "plugin_data": null, "size": 8903, "suppressed": [], "version_id": "1.15.0"}