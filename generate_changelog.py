#!/usr/bin/env python3
"""
Changelog Generator

Creates a changelog document for the Mathematical Algorithms Project
in HTML format that can be easily converted to Word document.
"""

import datetime
from typing import List, Dict, Any


def generate_changelog_html() -> str:
    """
    Generate HTML content for the changelog.
    
    Returns:
        HTML string containing the complete changelog
    """
    
    # Project information
    project_name = "Mathematical Algorithms Project"
    current_date = datetime.datetime.now().strftime("%B %d, %Y")
    
    # Changelog data structure
    changelog_data = [
        {
            "version": "2.0.0",
            "date": "2025-01-06",
            "type": "Major Release",
            "summary": "Added comprehensive trigonometric calculator with multiple algorithms",
            "changes": [
                {
                    "category": "New Features",
                    "items": [
                        "Added trigonometric functions calculator (trig_calculator.py)",
                        "Implemented Taylor series expansion for sin, cos, tan",
                        "Implemented CORDIC algorithm for trigonometric calculations",
                        "Added π approximation using <PERSON><PERSON><PERSON><PERSON> formula",
                        "Added angle normalization and conversion functions",
                        "Created interactive mode for trigonometric calculations",
                        "Added algorithm comparison functionality"
                    ]
                },
                {
                    "category": "Technical Improvements",
                    "items": [
                        "Built mathematical functions from scratch (factorial, power, absolute value)",
                        "Implemented high-precision calculations with configurable terms",
                        "Added early termination optimization for series convergence",
                        "Created comprehensive error handling for undefined cases"
                    ]
                },
                {
                    "category": "Documentation",
                    "items": [
                        "Added detailed function documentation with mathematical formulas",
                        "Included algorithm comparison and performance notes",
                        "Created usage examples for both programmatic and interactive use"
                    ]
                }
            ]
        },
        {
            "version": "1.2.0",
            "date": "2025-01-06",
            "type": "Feature Release",
            "summary": "Enhanced prime calculator with alternate algorithms and improved functionality",
            "changes": [
                {
                    "category": "New Algorithms",
                    "items": [
                        "Added Sieve of Sundaram as alternative to Sieve of Eratosthenes",
                        "Implemented Miller-Rabin probabilistic primality test",
                        "Added algorithm comparison in demonstration mode"
                    ]
                },
                {
                    "category": "Enhanced Features",
                    "items": [
                        "Updated interactive mode with new commands (miller, sundaram)",
                        "Added performance demonstration for large numbers",
                        "Enhanced main demo to compare algorithm results",
                        "Improved command-line interface with better descriptions"
                    ]
                },
                {
                    "category": "Bug Fixes",
                    "items": [
                        "Fixed type annotation for 'primes' variable (list[int])",
                        "Corrected f-string usage without placeholders"
                    ]
                },
                {
                    "category": "Documentation",
                    "items": [
                        "Updated comprehensive README.md with algorithm comparison table",
                        "Added usage examples and technical details",
                        "Documented all new algorithms and their use cases",
                        "Created algorithm performance comparison guide"
                    ]
                }
            ]
        },
        {
            "version": "1.1.0",
            "date": "2025-01-05",
            "type": "Documentation Release",
            "summary": "Comprehensive documentation update and project structure improvements",
            "changes": [
                {
                    "category": "Documentation",
                    "items": [
                        "Created comprehensive README.md with full project documentation",
                        "Added algorithm comparison table with time complexities",
                        "Included usage examples and code snippets",
                        "Added technical details and type annotation information",
                        "Created contributing guidelines and future enhancement suggestions"
                    ]
                },
                {
                    "category": "Project Structure",
                    "items": [
                        "Organized file structure documentation",
                        "Added requirements and compatibility information",
                        "Established project licensing information"
                    ]
                }
            ]
        },
        {
            "version": "1.0.0",
            "date": "2025-01-04",
            "type": "Initial Release",
            "summary": "Initial implementation of prime number calculator with core algorithms",
            "changes": [
                {
                    "category": "Core Features",
                    "items": [
                        "Implemented basic prime number checking (trial division)",
                        "Added Sieve of Eratosthenes for finding primes up to a limit",
                        "Created sequential prime number generation",
                        "Implemented prime factorization algorithm"
                    ]
                },
                {
                    "category": "User Interface",
                    "items": [
                        "Created interactive command-line interface",
                        "Added demonstration mode with common test cases",
                        "Implemented error handling and input validation"
                    ]
                },
                {
                    "category": "Code Quality",
                    "items": [
                        "Added comprehensive type annotations",
                        "Implemented proper error handling",
                        "Created modular function structure",
                        "Added detailed docstrings for all functions"
                    ]
                }
            ]
        }
    ]
    
    # Generate HTML
    html_content = f"""
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{project_name} - Changelog</title>
    <style>
        body {{
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            color: #333;
        }}
        .header {{
            text-align: center;
            border-bottom: 3px solid #2c3e50;
            padding-bottom: 20px;
            margin-bottom: 30px;
        }}
        .header h1 {{
            color: #2c3e50;
            margin-bottom: 10px;
            font-size: 2.5em;
        }}
        .header .subtitle {{
            color: #7f8c8d;
            font-size: 1.2em;
            font-style: italic;
        }}
        .version-block {{
            margin-bottom: 40px;
            border: 1px solid #bdc3c7;
            border-radius: 8px;
            overflow: hidden;
        }}
        .version-header {{
            background: linear-gradient(135deg, #3498db, #2980b9);
            color: white;
            padding: 15px 20px;
            margin: 0;
        }}
        .version-header h2 {{
            margin: 0;
            font-size: 1.5em;
        }}
        .version-info {{
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
        }}
        .version-meta {{
            font-size: 0.9em;
            opacity: 0.9;
        }}
        .version-content {{
            padding: 20px;
        }}
        .summary {{
            background-color: #ecf0f1;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
            font-style: italic;
            border-left: 4px solid #3498db;
        }}
        .category {{
            margin-bottom: 25px;
        }}
        .category h3 {{
            color: #2c3e50;
            border-bottom: 2px solid #3498db;
            padding-bottom: 5px;
            margin-bottom: 15px;
            font-size: 1.2em;
        }}
        .category ul {{
            list-style-type: none;
            padding-left: 0;
        }}
        .category li {{
            background-color: #f8f9fa;
            margin-bottom: 8px;
            padding: 10px 15px;
            border-radius: 5px;
            border-left: 3px solid #27ae60;
            position: relative;
        }}
        .category li:before {{
            content: "✓";
            color: #27ae60;
            font-weight: bold;
            position: absolute;
            left: -15px;
        }}
        .stats {{
            background-color: #f1f2f6;
            padding: 20px;
            border-radius: 8px;
            margin-top: 30px;
            text-align: center;
        }}
        .stats h3 {{
            color: #2c3e50;
            margin-bottom: 15px;
        }}
        .stats-grid {{
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-top: 15px;
        }}
        .stat-item {{
            background: white;
            padding: 15px;
            border-radius: 5px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }}
        .stat-number {{
            font-size: 2em;
            font-weight: bold;
            color: #3498db;
        }}
        .stat-label {{
            color: #7f8c8d;
            font-size: 0.9em;
        }}
        @media print {{
            body {{ font-size: 12pt; }}
            .version-block {{ page-break-inside: avoid; }}
        }}
    </style>
</head>
<body>
    <div class="header">
        <h1>{project_name}</h1>
        <div class="subtitle">What's New & Changelog</div>
        <div class="subtitle">Generated on {current_date}</div>
    </div>
"""
    
    # Add version blocks
    for version_data in changelog_data:
        html_content += f"""
    <div class="version-block">
        <div class="version-header">
            <div class="version-info">
                <h2>Version {version_data['version']}</h2>
                <div class="version-meta">
                    <div>{version_data['date']} • {version_data['type']}</div>
                </div>
            </div>
        </div>
        <div class="version-content">
            <div class="summary">
                <strong>Summary:</strong> {version_data['summary']}
            </div>
"""
        
        # Add categories and changes
        for category in version_data['changes']:
            html_content += f"""
            <div class="category">
                <h3>{category['category']}</h3>
                <ul>
"""
            for item in category['items']:
                html_content += f"                    <li>{item}</li>\n"
            
            html_content += """                </ul>
            </div>
"""
        
        html_content += """        </div>
    </div>
"""
    
    # Add project statistics
    total_features = sum(len(cat['items']) for version in changelog_data for cat in version['changes'])
    total_versions = len(changelog_data)
    
    html_content += f"""
    <div class="stats">
        <h3>Project Statistics</h3>
        <div class="stats-grid">
            <div class="stat-item">
                <div class="stat-number">{total_versions}</div>
                <div class="stat-label">Total Releases</div>
            </div>
            <div class="stat-item">
                <div class="stat-number">{total_features}</div>
                <div class="stat-label">Features & Improvements</div>
            </div>
            <div class="stat-item">
                <div class="stat-number">2</div>
                <div class="stat-label">Core Modules</div>
            </div>
            <div class="stat-item">
                <div class="stat-number">6</div>
                <div class="stat-label">Algorithms Implemented</div>
            </div>
        </div>
    </div>

    <div style="margin-top: 40px; text-align: center; color: #7f8c8d; font-size: 0.9em;">
        <p>This changelog documents the evolution of the Mathematical Algorithms Project.</p>
        <p>For technical details and usage instructions, please refer to the README.md file.</p>
    </div>

</body>
</html>
"""
    
    return html_content


def main():
    """
    Generate and save the changelog HTML file.
    """
    print("Generating changelog document...")
    
    # Generate HTML content
    html_content = generate_changelog_html()
    
    # Save to file
    filename = "CHANGELOG.html"
    with open(filename, 'w', encoding='utf-8') as f:
        f.write(html_content)
    
    print(f"Changelog generated successfully: {filename}")
    print("\nTo convert to Word document:")
    print("1. Open the HTML file in a web browser")
    print("2. Print the page and select 'Save as PDF' or 'Microsoft Print to PDF'")
    print("3. Open the PDF in Microsoft Word and save as .docx")
    print("\nAlternatively, you can copy the content from the browser and paste into Word.")


if __name__ == "__main__":
    main()
