#!/usr/bin/env python3
"""
Prime Number Calculator

A simple Python script for prime number calculations including:
- Checking if a number is prime
- Finding all primes up to a given number (Sieve of Eratosthenes)
- Generating the first N prime numbers
- Finding prime factors of a number
"""

import math
from typing import List


def is_prime(n: int) -> bool:
    """
    Check if a number is prime.
    
    Args:
        n: The number to check
        
    Returns:
        True if the number is prime, False otherwise
    """
    if n < 2:
        return False
    if n == 2:
        return True
    if n % 2 == 0:
        return False
    
    # Check odd divisors up to sqrt(n)
    for i in range(3, int(math.sqrt(n)) + 1, 2):
        if n % i == 0:
            return False
    return True


def sieve_of_eratosthenes(limit: int) -> List[int]:
    """
    Find all prime numbers up to a given limit using the Sieve of Eratosthenes.
    
    Args:
        limit: The upper limit to find primes up to
        
    Returns:
        List of all prime numbers up to the limit
    """
    if limit < 2:
        return []
    
    # Create a boolean array and initialize all entries as True
    sieve = [True] * (limit + 1)
    sieve[0] = sieve[1] = False
    
    for i in range(2, int(math.sqrt(limit)) + 1):
        if sieve[i]:
            # Mark all multiples of i as not prime
            for j in range(i * i, limit + 1, i):
                sieve[j] = False
    
    # Collect all prime numbers
    return [i for i in range(2, limit + 1) if sieve[i]]


def first_n_primes(n: int) -> List[int]:
    """
    Generate the first N prime numbers.
    
    Args:
        n: Number of primes to generate
        
    Returns:
        List of the first N prime numbers
    """
    if n <= 0:
        return []
    
    primes = []
    candidate = 2
    
    while len(primes) < n:
        if is_prime(candidate):
            primes.append(candidate)
        candidate += 1
    
    return primes


def prime_factors(n: int) -> List[int]:
    """
    Find all prime factors of a number.
    
    Args:
        n: The number to factorize
        
    Returns:
        List of prime factors (with repetition)
    """
    if n <= 1:
        return []
    
    factors = []
    
    # Handle factor 2
    while n % 2 == 0:
        factors.append(2)
        n //= 2
    
    # Handle odd factors
    for i in range(3, int(math.sqrt(n)) + 1, 2):
        while n % i == 0:
            factors.append(i)
            n //= i
    
    # If n is still greater than 2, it's a prime factor
    if n > 2:
        factors.append(n)
    
    return factors


def main():
    """
    Main function to demonstrate the prime number calculations.
    """
    print("Prime Number Calculator")
    print("=" * 30)
    
    # Test individual number
    test_num = 17
    print(f"\nIs {test_num} prime? {is_prime(test_num)}")
    
    # Find primes up to a limit
    limit = 30
    primes_up_to_limit = sieve_of_eratosthenes(limit)
    print(f"\nPrimes up to {limit}: {primes_up_to_limit}")
    
    # First N primes
    n = 10
    first_primes = first_n_primes(n)
    print(f"\nFirst {n} primes: {first_primes}")
    
    # Prime factorization
    factor_num = 60
    factors = prime_factors(factor_num)
    print(f"\nPrime factors of {factor_num}: {factors}")
    print(f"Verification: {' × '.join(map(str, factors))} = {math.prod(factors)}")
    
    # Interactive mode
    print("\n" + "=" * 30)
    print("Interactive Mode")
    print("Commands:")
    print("  check <number>     - Check if a number is prime")
    print("  sieve <limit>      - Find all primes up to limit")
    print("  first <count>      - Get first N primes")
    print("  factors <number>   - Get prime factors")
    print("  quit               - Exit")
    
    while True:
        try:
            command = input("\nEnter command: ").strip().lower()
            
            if command == "quit":
                break
            
            parts = command.split()
            if len(parts) != 2:
                print("Invalid command format. Use: <command> <number>")
                continue
            
            cmd, value = parts
            num = int(value)
            
            if cmd == "check":
                result = is_prime(num)
                print(f"{num} is {'prime' if result else 'not prime'}")
            elif cmd == "sieve":
                result = sieve_of_eratosthenes(num)
                print(f"Primes up to {num}: {result}")
            elif cmd == "first":
                result = first_n_primes(num)
                print(f"First {num} primes: {result}")
            elif cmd == "factors":
                result = prime_factors(num)
                print(f"Prime factors of {num}: {result}")
            else:
                print("Unknown command. Use: check, sieve, first, factors, or quit")
                
        except ValueError:
            print("Please enter a valid number")
        except KeyboardInterrupt:
            print("\nGoodbye!")
            break
        except Exception as e:
            print(f"Error: {e}")


if __name__ == "__main__":
    main()
