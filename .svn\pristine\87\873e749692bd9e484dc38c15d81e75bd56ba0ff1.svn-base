#!/usr/bin/env python3
"""
Prime Number Calculator

A comprehensive Python script for prime number calculations including:
- Checking if a number is prime (basic trial division)
- Checking if a number is prime (Miller<PERSON><PERSON> probabilistic test)
- Finding all primes up to a given number (<PERSON><PERSON> of Eratosthenes)
- Finding all primes up to a given number (<PERSON><PERSON> of Sundaram)
- Generating the first N prime numbers
- Finding prime factors of a number

Features multiple algorithms for comparison and different use cases.
"""

import math
import random
from typing import List


def is_prime(n: int) -> bool:
    """
    Check if a number is prime.
    
    Args:
        n: The number to check
        
    Returns:
        True if the number is prime, False otherwise
    """
    if n < 2:
        return False
    if n == 2:
        return True
    if n % 2 == 0:
        return False
    
    # Check odd divisors up to sqrt(n)
    for i in range(3, int(math.sqrt(n)) + 1, 2):
        if n % i == 0:
            return False
    return True


def miller_rabin_is_prime(n: int, k: int = 5) -> bool:
    """
    Miller-Rabin primality test - a probabilistic algorithm.

    This is an alternate algorithm to the basic trial division method.
    It's much faster for large numbers but has a small probability of error.

    Args:
        n: The number to check for primality
        k: Number of rounds of testing (higher k = more accurate, default=5)

    Returns:
        True if the number is probably prime, False if definitely composite
    """
    if n < 2:
        return False
    if n == 2 or n == 3:
        return True
    if n % 2 == 0:
        return False

    # Write n-1 as d * 2^r
    r = 0
    d = n - 1
    while d % 2 == 0:
        r += 1
        d //= 2

    # Perform k rounds of testing
    for _ in range(k):
        a = random.randrange(2, n - 1)
        x = pow(a, d, n)  # a^d mod n

        if x == 1 or x == n - 1:
            continue

        for _ in range(r - 1):
            x = pow(x, 2, n)
            if x == n - 1:
                break
        else:
            return False

    return True


def sieve_of_eratosthenes(limit: int) -> List[int]:
    """
    Find all prime numbers up to a given limit using the Sieve of Eratosthenes.
    
    Args:
        limit: The upper limit to find primes up to
        
    Returns:
        List of all prime numbers up to the limit
    """
    if limit < 2:
        return []
    
    # Create a boolean array and initialize all entries as True
    sieve = [True] * (limit + 1)
    sieve[0] = sieve[1] = False
    
    for i in range(2, int(math.sqrt(limit)) + 1):
        if sieve[i]:
            # Mark all multiples of i as not prime
            for j in range(i * i, limit + 1, i):
                sieve[j] = False
    
    # Collect all prime numbers
    return [i for i in range(2, limit + 1) if sieve[i]]


def sieve_of_sundaram(limit: int) -> List[int]:
    """
    Find all prime numbers up to a given limit using the Sieve of Sundaram.

    This is an alternate algorithm to the Sieve of Eratosthenes.
    The Sieve of Sundaram generates all odd primes up to a given limit.

    Args:
        limit: The upper limit to find primes up to

    Returns:
        List of all prime numbers up to the limit
    """
    if limit < 2:
        return []
    if limit == 2:
        return [2]

    # Calculate the range for the sieve
    n = (limit - 1) // 2

    # Create a boolean array and initialize all entries as False
    # marked[i] represents whether 2*i+1 is composite
    marked = [False] * (n + 1)

    # Mark numbers of the form i + j + 2*i*j where i <= j
    for i in range(1, n + 1):
        j = i
        while i + j + 2 * i * j <= n:
            marked[i + j + 2 * i * j] = True
            j += 1

    # Collect all prime numbers
    primes = [2]  # 2 is the only even prime
    for i in range(1, n + 1):
        if not marked[i]:
            primes.append(2 * i + 1)

    return primes


def first_n_primes(n: int) -> List[int]:
    """
    Generate the first N prime numbers.
    
    Args:
        n: Number of primes to generate
        
    Returns:
        List of the first N prime numbers
    """
    if n <= 0:
        return []
    
    primes: list[int] = []
    candidate = 2
    
    while len(primes) < n:
        if is_prime(candidate):
            primes.append(candidate)
        candidate += 1
    
    return primes


def prime_factors(n: int) -> List[int]:
    """
    Find all prime factors of a number.
    
    Args:
        n: The number to factorize
        
    Returns:
        List of prime factors (with repetition)
    """
    if n <= 1:
        return []
    
    factors = []
    
    # Handle factor 2
    while n % 2 == 0:
        factors.append(2)
        n //= 2
    
    # Handle odd factors
    for i in range(3, int(math.sqrt(n)) + 1, 2):
        while n % i == 0:
            factors.append(i)
            n //= i
    
    # If n is still greater than 2, it's a prime factor
    if n > 2:
        factors.append(n)
    
    return factors


def main():
    """
    Main function to demonstrate the prime number calculations.
    """
    print("Prime Number Calculator")
    print("=" * 30)
    
    # Test individual number with different algorithms
    test_num = 17
    basic_result = is_prime(test_num)
    miller_rabin_result = miller_rabin_is_prime(test_num)
    print(f"\nIs {test_num} prime?")
    print(f"  Basic algorithm: {basic_result}")
    print(f"  Miller-Rabin: {miller_rabin_result}")

    # Test with a larger number to show Miller-Rabin's efficiency
    large_num = 982451653
    print(f"\nTesting larger number {large_num}:")
    print(f"  Miller-Rabin: {miller_rabin_is_prime(large_num)}")
    print("  (Miller-Rabin is much faster for large numbers)")
    
    # Find primes up to a limit using different algorithms
    limit = 30
    primes_eratosthenes = sieve_of_eratosthenes(limit)
    primes_sundaram = sieve_of_sundaram(limit)
    print(f"\nPrimes up to {limit} (Sieve of Eratosthenes): {primes_eratosthenes}")
    print(f"Primes up to {limit} (Sieve of Sundaram): {primes_sundaram}")
    print(f"Both algorithms agree: {primes_eratosthenes == primes_sundaram}")
    
    # First N primes
    n = 10
    first_primes = first_n_primes(n)
    print(f"\nFirst {n} primes: {first_primes}")
    
    # Prime factorization
    factor_num = 60
    factors = prime_factors(factor_num)
    print(f"\nPrime factors of {factor_num}: {factors}")
    print(f"Verification: {' × '.join(map(str, factors))} = {math.prod(factors)}")
    
    # Interactive mode
    print("\n" + "=" * 30)
    print("Interactive Mode")
    print("Commands:")
    print("  check <number>     - Check if a number is prime (basic algorithm)")
    print("  miller <number>    - Check if a number is prime (Miller-Rabin)")
    print("  sieve <limit>      - Find all primes up to limit (Eratosthenes)")
    print("  sundaram <limit>   - Find all primes up to limit (Sundaram)")
    print("  first <count>      - Get first N primes")
    print("  factors <number>   - Get prime factors")
    print("  quit               - Exit")
    
    while True:
        try:
            command = input("\nEnter command: ").strip().lower()
            
            if command == "quit":
                break
            
            parts = command.split()
            if len(parts) != 2:
                print("Invalid command format. Use: <command> <number>")
                continue
            
            cmd, value = parts
            num = int(value)
            
            if cmd == "check":
                result = is_prime(num)
                print(f"{num} is {'prime' if result else 'not prime'} (basic algorithm)")
            elif cmd == "miller":
                result = miller_rabin_is_prime(num)
                print(f"{num} is {'probably prime' if result else 'composite'} (Miller-Rabin)")
            elif cmd == "sieve":
                result = sieve_of_eratosthenes(num)
                print(f"Primes up to {num} (Eratosthenes): {result}")
            elif cmd == "sundaram":
                result = sieve_of_sundaram(num)
                print(f"Primes up to {num} (Sundaram): {result}")
            elif cmd == "first":
                result = first_n_primes(num)
                print(f"First {num} primes: {result}")
            elif cmd == "factors":
                result = prime_factors(num)
                print(f"Prime factors of {num}: {result}")
            else:
                print("Unknown command. Use: check, miller, sieve, sundaram, first, factors, or quit")
                
        except ValueError:
            print("Please enter a valid number")
        except KeyboardInterrupt:
            print("\nGoodbye!")
            break
        except Exception as e:
            print(f"Error: {e}")


if __name__ == "__main__":
    main()
