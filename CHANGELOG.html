
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Mathematical Algorithms Project - Changelog</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            color: #333;
        }
        .header {
            text-align: center;
            border-bottom: 3px solid #2c3e50;
            padding-bottom: 20px;
            margin-bottom: 30px;
        }
        .header h1 {
            color: #2c3e50;
            margin-bottom: 10px;
            font-size: 2.5em;
        }
        .header .subtitle {
            color: #7f8c8d;
            font-size: 1.2em;
            font-style: italic;
        }
        .version-block {
            margin-bottom: 40px;
            border: 1px solid #bdc3c7;
            border-radius: 8px;
            overflow: hidden;
        }
        .version-header {
            background: linear-gradient(135deg, #3498db, #2980b9);
            color: white;
            padding: 15px 20px;
            margin: 0;
        }
        .version-header h2 {
            margin: 0;
            font-size: 1.5em;
        }
        .version-info {
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
        }
        .version-meta {
            font-size: 0.9em;
            opacity: 0.9;
        }
        .version-content {
            padding: 20px;
        }
        .summary {
            background-color: #ecf0f1;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
            font-style: italic;
            border-left: 4px solid #3498db;
        }
        .category {
            margin-bottom: 25px;
        }
        .category h3 {
            color: #2c3e50;
            border-bottom: 2px solid #3498db;
            padding-bottom: 5px;
            margin-bottom: 15px;
            font-size: 1.2em;
        }
        .category ul {
            list-style-type: none;
            padding-left: 0;
        }
        .category li {
            background-color: #f8f9fa;
            margin-bottom: 8px;
            padding: 10px 15px;
            border-radius: 5px;
            border-left: 3px solid #27ae60;
            position: relative;
        }
        .category li:before {
            content: "✓";
            color: #27ae60;
            font-weight: bold;
            position: absolute;
            left: -15px;
        }
        .stats {
            background-color: #f1f2f6;
            padding: 20px;
            border-radius: 8px;
            margin-top: 30px;
            text-align: center;
        }
        .stats h3 {
            color: #2c3e50;
            margin-bottom: 15px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-top: 15px;
        }
        .stat-item {
            background: white;
            padding: 15px;
            border-radius: 5px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        .stat-number {
            font-size: 2em;
            font-weight: bold;
            color: #3498db;
        }
        .stat-label {
            color: #7f8c8d;
            font-size: 0.9em;
        }
        @media print {
            body { font-size: 12pt; }
            .version-block { page-break-inside: avoid; }
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>Mathematical Algorithms Project</h1>
        <div class="subtitle">What's New & Changelog</div>
        <div class="subtitle">Generated on August 06, 2025</div>
    </div>

    <div class="version-block">
        <div class="version-header">
            <div class="version-info">
                <h2>Version 2.0.0</h2>
                <div class="version-meta">
                    <div>2025-01-06 • Major Release</div>
                </div>
            </div>
        </div>
        <div class="version-content">
            <div class="summary">
                <strong>Summary:</strong> Added comprehensive trigonometric calculator with multiple algorithms
            </div>

            <div class="category">
                <h3>New Features</h3>
                <ul>
                    <li>Added trigonometric functions calculator (trig_calculator.py)</li>
                    <li>Implemented Taylor series expansion for sin, cos, tan</li>
                    <li>Implemented CORDIC algorithm for trigonometric calculations</li>
                    <li>Added π approximation using Leibniz formula</li>
                    <li>Added angle normalization and conversion functions</li>
                    <li>Created interactive mode for trigonometric calculations</li>
                    <li>Added algorithm comparison functionality</li>
                </ul>
            </div>

            <div class="category">
                <h3>Technical Improvements</h3>
                <ul>
                    <li>Built mathematical functions from scratch (factorial, power, absolute value)</li>
                    <li>Implemented high-precision calculations with configurable terms</li>
                    <li>Added early termination optimization for series convergence</li>
                    <li>Created comprehensive error handling for undefined cases</li>
                </ul>
            </div>

            <div class="category">
                <h3>Documentation</h3>
                <ul>
                    <li>Added detailed function documentation with mathematical formulas</li>
                    <li>Included algorithm comparison and performance notes</li>
                    <li>Created usage examples for both programmatic and interactive use</li>
                </ul>
            </div>
        </div>
    </div>

    <div class="version-block">
        <div class="version-header">
            <div class="version-info">
                <h2>Version 1.2.0</h2>
                <div class="version-meta">
                    <div>2025-01-06 • Feature Release</div>
                </div>
            </div>
        </div>
        <div class="version-content">
            <div class="summary">
                <strong>Summary:</strong> Enhanced prime calculator with alternate algorithms and improved functionality
            </div>

            <div class="category">
                <h3>New Algorithms</h3>
                <ul>
                    <li>Added Sieve of Sundaram as alternative to Sieve of Eratosthenes</li>
                    <li>Implemented Miller-Rabin probabilistic primality test</li>
                    <li>Added algorithm comparison in demonstration mode</li>
                </ul>
            </div>

            <div class="category">
                <h3>Enhanced Features</h3>
                <ul>
                    <li>Updated interactive mode with new commands (miller, sundaram)</li>
                    <li>Added performance demonstration for large numbers</li>
                    <li>Enhanced main demo to compare algorithm results</li>
                    <li>Improved command-line interface with better descriptions</li>
                </ul>
            </div>

            <div class="category">
                <h3>Bug Fixes</h3>
                <ul>
                    <li>Fixed type annotation for 'primes' variable (list[int])</li>
                    <li>Corrected f-string usage without placeholders</li>
                </ul>
            </div>

            <div class="category">
                <h3>Documentation</h3>
                <ul>
                    <li>Updated comprehensive README.md with algorithm comparison table</li>
                    <li>Added usage examples and technical details</li>
                    <li>Documented all new algorithms and their use cases</li>
                    <li>Created algorithm performance comparison guide</li>
                </ul>
            </div>
        </div>
    </div>

    <div class="version-block">
        <div class="version-header">
            <div class="version-info">
                <h2>Version 1.1.0</h2>
                <div class="version-meta">
                    <div>2025-01-05 • Documentation Release</div>
                </div>
            </div>
        </div>
        <div class="version-content">
            <div class="summary">
                <strong>Summary:</strong> Comprehensive documentation update and project structure improvements
            </div>

            <div class="category">
                <h3>Documentation</h3>
                <ul>
                    <li>Created comprehensive README.md with full project documentation</li>
                    <li>Added algorithm comparison table with time complexities</li>
                    <li>Included usage examples and code snippets</li>
                    <li>Added technical details and type annotation information</li>
                    <li>Created contributing guidelines and future enhancement suggestions</li>
                </ul>
            </div>

            <div class="category">
                <h3>Project Structure</h3>
                <ul>
                    <li>Organized file structure documentation</li>
                    <li>Added requirements and compatibility information</li>
                    <li>Established project licensing information</li>
                </ul>
            </div>
        </div>
    </div>

    <div class="version-block">
        <div class="version-header">
            <div class="version-info">
                <h2>Version 1.0.0</h2>
                <div class="version-meta">
                    <div>2025-01-04 • Initial Release</div>
                </div>
            </div>
        </div>
        <div class="version-content">
            <div class="summary">
                <strong>Summary:</strong> Initial implementation of prime number calculator with core algorithms
            </div>

            <div class="category">
                <h3>Core Features</h3>
                <ul>
                    <li>Implemented basic prime number checking (trial division)</li>
                    <li>Added Sieve of Eratosthenes for finding primes up to a limit</li>
                    <li>Created sequential prime number generation</li>
                    <li>Implemented prime factorization algorithm</li>
                </ul>
            </div>

            <div class="category">
                <h3>User Interface</h3>
                <ul>
                    <li>Created interactive command-line interface</li>
                    <li>Added demonstration mode with common test cases</li>
                    <li>Implemented error handling and input validation</li>
                </ul>
            </div>

            <div class="category">
                <h3>Code Quality</h3>
                <ul>
                    <li>Added comprehensive type annotations</li>
                    <li>Implemented proper error handling</li>
                    <li>Created modular function structure</li>
                    <li>Added detailed docstrings for all functions</li>
                </ul>
            </div>
        </div>
    </div>

    <div class="stats">
        <h3>Project Statistics</h3>
        <div class="stats-grid">
            <div class="stat-item">
                <div class="stat-number">4</div>
                <div class="stat-label">Total Releases</div>
            </div>
            <div class="stat-item">
                <div class="stat-number">46</div>
                <div class="stat-label">Features & Improvements</div>
            </div>
            <div class="stat-item">
                <div class="stat-number">2</div>
                <div class="stat-label">Core Modules</div>
            </div>
            <div class="stat-item">
                <div class="stat-number">6</div>
                <div class="stat-label">Algorithms Implemented</div>
            </div>
        </div>
    </div>

    <div style="margin-top: 40px; text-align: center; color: #7f8c8d; font-size: 0.9em;">
        <p>This changelog documents the evolution of the Mathematical Algorithms Project.</p>
        <p>For technical details and usage instructions, please refer to the README.md file.</p>
    </div>

</body>
</html>
