{".class": "MypyFile", "_fullname": "trig_calculator", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "PI": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "trig_calculator.PI", "name": "PI", "type": "builtins.float"}}, "Union": {".class": "SymbolTableNode", "cross_ref": "typing.Union", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "trig_calculator.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "trig_calculator.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "trig_calculator.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "trig_calculator.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "trig_calculator.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "trig_calculator.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "absolute_value": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["x"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "trig_calculator.absolute_value", "name": "absolute_value", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["x"], "arg_types": ["builtins.float"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "absolute_value", "ret_type": "builtins.float", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "compare_algorithms": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["angle_degrees"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "trig_calculator.compare_algorithms", "name": "compare_algorithms", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["angle_degrees"], "arg_types": ["builtins.float"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "compare_algorithms", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "cos_cordic": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["x", "iterations"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "trig_calculator.cos_cordic", "name": "cos_cordic", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["x", "iterations"], "arg_types": ["builtins.float", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "cos_cordic", "ret_type": "builtins.float", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "cos_taylor": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["x", "terms"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "trig_calculator.cos_taylor", "name": "cos_taylor", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["x", "terms"], "arg_types": ["builtins.float", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "cos_taylor", "ret_type": "builtins.float", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "degrees_to_radians": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["degrees"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "trig_calculator.degrees_to_radians", "name": "degrees_to_radians", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["degrees"], "arg_types": ["builtins.float"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "degrees_to_radians", "ret_type": "builtins.float", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "factorial": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["n"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "trig_calculator.factorial", "name": "factorial", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["n"], "arg_types": ["builtins.int"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "factorial", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "main": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "trig_calculator.main", "name": "main", "type": null}}, "normalize_angle": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["angle"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "trig_calculator.normalize_angle", "name": "normalize_angle", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["angle"], "arg_types": ["builtins.float"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "normalize_angle", "ret_type": "builtins.float", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "pi_approximation": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [1], "arg_names": ["precision"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "trig_calculator.pi_approximation", "name": "pi_approximation", "type": {".class": "CallableType", "arg_kinds": [1], "arg_names": ["precision"], "arg_types": ["builtins.int"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "pi_approximation", "ret_type": "builtins.float", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "power": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["base", "exponent"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "trig_calculator.power", "name": "power", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["base", "exponent"], "arg_types": ["builtins.float", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "power", "ret_type": "builtins.float", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "radians_to_degrees": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["radians"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "trig_calculator.radians_to_degrees", "name": "radians_to_degrees", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["radians"], "arg_types": ["builtins.float"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "radians_to_degrees", "ret_type": "builtins.float", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "sin_cordic": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["x", "iterations"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "trig_calculator.sin_cordic", "name": "sin_cordic", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["x", "iterations"], "arg_types": ["builtins.float", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "sin_cordic", "ret_type": "builtins.float", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "sin_taylor": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["x", "terms"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "trig_calculator.sin_taylor", "name": "sin_taylor", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["x", "terms"], "arg_types": ["builtins.float", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "sin_taylor", "ret_type": "builtins.float", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "tan_from_sin_cos": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["x", "method"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "trig_calculator.tan_from_sin_cos", "name": "tan_from_sin_cos", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["x", "method"], "arg_types": ["builtins.float", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "tan_from_sin_cos", "ret_type": "builtins.float", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "path": "C:\\home-repos\\svn_monitor_server_test_repo\\trig_calculator.py"}